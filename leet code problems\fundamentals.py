# Two sum
class Solution(object):
    def twoSum(self, nums, target):
        """
        :type nums: List[int]
        :type target: int
        :rtype: List[int]
        """
        # Use a hash map to store number and its index
        num_map = {}

        for i, num in enumerate(nums):
            complement = target - num
            if complement in num_map:
                return [num_map[complement], i]
            num_map[num] = i

        return []  # Return empty list if no solution found

# Test the solution
s = Solution()
print(s.twoSum([2,7,11,15], 9))

# find the first and last position of a given element
class Solution(object):
    def searchRange(self, nums, target):
        """
        :type nums: List[int]
        :type target: int
        :rtype: List[int]
        """
        if not nums or target not in nums:
            return [-1, -1]

        # Find first position
        first_pos = -1
        for i in range(len(nums)):
            if nums[i] == target:
                first_pos = i
                break

        # Find last position
        last_pos = -1
        for i in range(len(nums) - 1, -1, -1):
            if nums[i] == target:
                last_pos = i
                break

        return [first_pos, last_pos]

s = Solution()
print(s.searchRange([5,7,7,8,8,10], 8))
    
# repeated DNA ssequence
class Solution(object):
    def findRepeatedDnaSequences(self, s):
        """
        :type s: str
        :rtype: List[str]
        """
        s_10=